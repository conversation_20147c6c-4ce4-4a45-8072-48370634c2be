<?php
declare(strict_types=1);

namespace Comave\Customer\Service\Validator;

use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberUtil;
use libphonenumber\PhoneNumberFormat;
use Comave\Customer\Exception\InvalidPhoneNumberException;

/**
 * Service class for validating and normalizing phone numbers using libphonenumber.
 */
class PhoneNumberValidator
{
    /**
     * The phone number utility instance from libphonenumber.
     */
    private readonly PhoneNumberUtil $util;
    
    /**
     * Constructor for PhoneNumberValidator.
     */
    public function __construct()
    {
        $this->util = PhoneNumberUtil::getInstance();
    }

    /**
     * Validates and normalizes a phone number string.
     *
     * @param string $raw The raw phone number string input
     * @return string The normalized E.164 formatted phone number
     * @throws InvalidPhoneNumberException If the phone number is invalid or cannot be parsed
     */
    public function validate(string $raw): string
    {
        try {
            $num = $this->util->parse($raw);

            if (!$this->util->isValidNumber($num)) {
                throw new InvalidPhoneNumberException();
            }

            return $this->util->format($num, PhoneNumberFormat::E164);
        } catch (NumberParseException $e) {
            throw new InvalidPhoneNumberException(null, $e);
        }
    }
}
