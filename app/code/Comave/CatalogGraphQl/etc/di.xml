<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\CatalogGraphQl\Model\Resolver\Products">
        <plugin name="apply_status_filter_plugin" type="Comave\CatalogGraphQl\Plugin\FilterEnabledProducts" />
    </type>
    <type name="Magento\CatalogGraphQl\Model\Resolver\Category\Products">
        <plugin name="removedisabled"
                type="Comave\CatalogGraphQl\Plugin\RemoveDisabledProducts"
                sortOrder="1"/>
    </type>
    <type name="Magento\CatalogGraphQl\Model\Resolver\Categories\DataProvider\Category\CompositeCollectionProcessor">
        <arguments>
            <argument name="collectionProcessors" xsi:type="array">
                <item name="comave_filter_empty_categories"
                      xsi:type="object">Comave\CatalogGraphQl\Model\Resolver\Categories\DataProvider\Category\FilterEmptyCategoriesProcessor</item>
            </argument>
        </arguments>
    </type>
</config>
