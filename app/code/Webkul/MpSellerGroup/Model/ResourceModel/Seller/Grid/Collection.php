<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerGroup
 * <AUTHOR>
 * @copyright Copyright (c) Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */

namespace Webkul\MpSellerGroup\Model\ResourceModel\Seller\Grid;

use Magento\Framework\Api\Search\SearchResultInterface;
use Magento\Framework\Search\AggregationInterface;
use Webkul\Marketplace\Model\ResourceModel\Seller\Collection as SellerCollection;
use AllowDynamicProperties;
/**
 * Class Collection
 * Collection for displaying grid of marketplace seller
 */
#[AllowDynamicProperties] class Collection extends \Webkul\Marketplace\Model\ResourceModel\Seller\Grid\Collection
{
    /**
     * Constructor
     *
     * @param \Magento\Framework\Data\Collection\EntityFactoryInterface $entityFactoryInterface
     * @param \Psr\Log\LoggerInterface $loggerInterface
     * @param \Magento\Framework\Data\Collection\Db\FetchStrategyInterface $fetchStrategyInterface
     * @param \Magento\Framework\Event\ManagerInterface $eventManagerInterface
     * @param \Magento\Store\Model\StoreManagerInterface $storeManagerInterface
     * @param mixed|null $mainTable
     * @param \Magento\Framework\Model\ResourceModel\Db\AbstractDb $eventPrefix
     * @param mixed $eventObject
     * @param mixed $resourceModel
     * @param \Webkul\MpSellerGroup\Model\SellerGroupTypeRepository $sellerGroupTypeRepository
     * @param \Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory $sellers
     * @param string $model
     * @param mixed $connection
     * @param \Magento\Framework\Model\ResourceModel\Db\AbstractDb|null $resource
     */
    public function __construct(
        \Magento\Framework\Data\Collection\EntityFactoryInterface $entityFactoryInterface,
        \Psr\Log\LoggerInterface $loggerInterface,
        \Magento\Framework\Data\Collection\Db\FetchStrategyInterface $fetchStrategyInterface,
        \Magento\Framework\Event\ManagerInterface $eventManagerInterface,
        \Magento\Store\Model\StoreManagerInterface $storeManagerInterface,
        $mainTable,
        $eventPrefix,
        $eventObject,
        $resourceModel,
        \Webkul\MpSellerGroup\Model\SellerGroupTypeRepository $sellerGroupTypeRepository,
        \Webkul\Marketplace\Model\ResourceModel\Seller\CollectionFactory $sellers,
        $model = \Magento\Framework\View\Element\UiComponent\DataProvider\Document::class,
        $connection = null,
        \Magento\Framework\Model\ResourceModel\Db\AbstractDb $resource = null
    ) {
        parent::__construct(
            $entityFactoryInterface,
            $loggerInterface,
            $fetchStrategyInterface,
            $eventManagerInterface,
            $storeManagerInterface,
            $mainTable,
            $eventPrefix,
            $eventObject,
            $resourceModel,
            $model,
            $connection,
            $resource
        );
        $this->sellers = $sellers;
        $this->sellerGroupTypeRepository = $sellerGroupTypeRepository;
    }

    /**
     * Function _renderFiltersBefore
     */
    protected function _renderFiltersBefore()
    {
        $groupTable = $this->getTable('marketplace_sellergroup_type');
        $groupTypeTable = $this->getTable('marketplace_sellergroup');
        $this->getSelect()->joinLeft(
            $groupTable.' as sgt',
            'main_table.seller_id = sgt.seller_id',
            [
                'group_id' => 'sgt.group_id'

            ]
        )
        ->joinLeft(
            $groupTypeTable.' as sg',
            'sgt.group_id = sg.entity_id',
            [
                'group_code' => 'sg.group_name'
            ]
        );

        // Debug: Log the SQL query to see what's happening
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/seller_grid_debug.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);
        $logger->info('Seller Grid SQL: ' . $this->getSelect()->__toString());
        $logger->info('Seller Grid Size: ' . $this->getSize());

        parent::_renderFiltersBefore();
    }

    /**
     * Debug method to log collection data
     */
    public function load($printQuery = false, $logQuery = false)
    {
        $result = parent::load($printQuery, $logQuery);

        // Debug: Log the loaded items
        $writer = new \Zend_Log_Writer_Stream(BP . '/var/log/seller_grid_debug.log');
        $logger = new \Zend_Log();
        $logger->addWriter($writer);

        $sellerIds = [];
        foreach ($this->getItems() as $item) {
            $sellerIds[] = $item->getSellerId();
        }

        $logger->info('Loaded Seller IDs: ' . implode(', ', $sellerIds));
        $logger->info('Total Items Loaded: ' . count($this->getItems()));

        return $result;
    }
}
