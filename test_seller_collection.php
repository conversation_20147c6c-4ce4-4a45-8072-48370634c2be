<?php
/**
 * Test script to debug seller collection
 * Run this from Magento root: php test_seller_collection.php
 */

use Magento\Framework\App\Bootstrap;

require __DIR__ . '/app/bootstrap.php';

$bootstrap = Bootstrap::create(BP, $_SERVER);
$objectManager = $bootstrap->getObjectManager();

echo "=== Testing Seller Collection ===\n";

try {
    // Test the original collection
    $originalCollection = $objectManager->create(\Webkul\Marketplace\Model\ResourceModel\Seller\Grid\Collection::class);
    echo "Original Collection Class: " . get_class($originalCollection) . "\n";
    
    // Test the overridden collection
    $collection = $objectManager->create(\Webkul\MpSellerGroup\Model\ResourceModel\Seller\Grid\Collection::class);
    echo "Overridden Collection Class: " . get_class($collection) . "\n";
    
    // Load the collection
    $collection->load();
    
    echo "Total sellers in collection: " . $collection->getSize() . "\n";
    echo "SQL Query: " . $collection->getSelect()->__toString() . "\n\n";
    
    echo "Sellers found:\n";
    foreach ($collection as $seller) {
        echo "- Seller ID: " . $seller->getSellerId() . 
             ", Name: " . $seller->getData('name') . 
             ", Email: " . $seller->getData('email') . 
             ", Store ID: " . $seller->getStoreId() . "\n";
    }
    
    // Test specifically for seller 788
    echo "\n=== Testing Seller 788 Specifically ===\n";
    $testCollection = $objectManager->create(\Webkul\MpSellerGroup\Model\ResourceModel\Seller\Grid\Collection::class);
    $testCollection->addFieldToFilter('main_table.seller_id', 788);
    $testCollection->load();
    
    echo "Seller 788 found: " . ($testCollection->getSize() > 0 ? 'YES' : 'NO') . "\n";
    if ($testCollection->getSize() > 0) {
        $seller788 = $testCollection->getFirstItem();
        echo "Seller 788 data: " . print_r($seller788->getData(), true) . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
