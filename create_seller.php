<?php
/**
 * Script to create a complete seller that will appear in admin grid
 * Run this from Magento root: php create_seller.php
 */

use Magento\Framework\App\Bootstrap;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterfaceFactory;
use Magento\Store\Model\StoreManagerInterface;

// Find Magento root directory
$magentoRoot = '/var/www/html';
if (!file_exists($magentoRoot . '/app/bootstrap.php')) {
    die("Error: Cannot find Magento bootstrap file at $magentoRoot/app/bootstrap.php\n");
}

require $magentoRoot . '/app/bootstrap.php';

$bootstrap = Bootstrap::create($magentoRoot, $_SERVER);
$objectManager = $bootstrap->getObjectManager();

echo "=== Creating New Seller ===\n";

try {
    $customerFactory = $objectManager->get(CustomerInterfaceFactory::class);
    $customerRepository = $objectManager->get(CustomerRepositoryInterface::class);
    $storeManager = $objectManager->get(StoreManagerInterface::class);
    $resourceConnection = $objectManager->get(\Magento\Framework\App\ResourceConnection::class);
    
    // Create customer first
    $customer = $customerFactory->create();
    $customer->setFirstname('Test');
    $customer->setLastname('Seller');
    $customer->setEmail('<EMAIL>');
    $customer->setWebsiteId($storeManager->getWebsite()->getId());
    $customer->setStoreId($storeManager->getStore()->getId());
    $customer->setGroupId(1); // General customer group
    
    // Save customer
    $savedCustomer = $customerRepository->save($customer, 'password123');
    $customerId = $savedCustomer->getId();
    
    echo "Created customer with ID: $customerId\n";
    
    // Get database connection
    $connection = $resourceConnection->getConnection();
    
    // Insert into marketplace_userdata for store_id = 0 (admin)
    $marketplaceData = [
        'is_seller' => 1,
        'seller_id' => $customerId,
        'store_id' => 0,
        'shop_url' => 'test-seller-' . $customerId,
        'shop_title' => 'Test Seller Shop',
        'kyb_status' => 'verified',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s'),
        'admin_notification' => 0,
        'allowed_categories' => '',
        'sub_account_permission' => '',
        'email' => '',
        'business_url' => '',
        'owner_name' => '',
        'business_country' => '',
        'business_city' => '',
        'business_street' => '',
        'business_postcode' => '',
        'ship_same_as_business' => 0,
        'ship_country' => '',
        'ship_city' => '',
        'ship_street' => '',
        'ship_postcode' => '',
        'vendor_group' => '',
        'business_registration_number' => ''
    ];
    
    $connection->insert('marketplace_userdata', $marketplaceData);
    echo "Inserted marketplace_userdata record for store_id = 0\n";
    
    // Insert into marketplace_userdata for store_id = 1 (default store)
    $marketplaceData['store_id'] = 1;
    $connection->insert('marketplace_userdata', $marketplaceData);
    echo "Inserted marketplace_userdata record for store_id = 1\n";
    
    // Reindex customer grid to make sure customer appears in customer_grid_flat
    $indexerFactory = $objectManager->get(\Magento\Indexer\Model\IndexerFactory::class);
    $customerGridIndexer = $indexerFactory->create()->load('customer_grid');
    $customerGridIndexer->reindexAll();
    echo "Reindexed customer grid\n";
    
    // Verify the seller was created properly
    $checkQuery = "
        SELECT 
            mu.seller_id,
            mu.is_seller,
            mu.store_id,
            mu.kyb_status,
            cgf.name,
            cgf.email
        FROM marketplace_userdata mu
        JOIN customer_grid_flat cgf ON mu.seller_id = cgf.entity_id
        WHERE mu.seller_id = ?
        ORDER BY mu.store_id
    ";
    
    $result = $connection->fetchAll($checkQuery, [$customerId]);
    
    echo "\nVerification Results:\n";
    foreach ($result as $row) {
        echo "- Seller ID: {$row['seller_id']}, Store ID: {$row['store_id']}, Status: {$row['is_seller']}, KYB: {$row['kyb_status']}, Name: {$row['name']}, Email: {$row['email']}\n";
    }
    
    echo "\n=== SUCCESS ===\n";
    echo "New seller created with ID: $customerId\n";
    echo "Email: <EMAIL>\n";
    echo "Password: password123\n";
    echo "Shop URL: test-seller-$customerId\n";
    echo "\nThe seller should now appear in the admin seller management grid.\n";
    echo "Please refresh the admin page and check.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
